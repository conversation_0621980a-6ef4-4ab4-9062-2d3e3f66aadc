#!/usr/bin/env python3
"""
Test script to verify that the Tesseract fix works with the problematic PDF
"""

import requests
import os
import time

# Configuration
API_BASE_URL = "http://localhost:8000"
PDF_FILE = "not_working_receipt.pdf"

def test_pdf_processing():
    """Test the PDF processing with the fixed Tesseract configuration"""
    
    print("Testing PDF processing with fixed Tesseract configuration...")
    print("=" * 60)
    
    # Check if the PDF file exists
    if not os.path.exists(PDF_FILE):
        print(f"❌ PDF file not found: {PDF_FILE}")
        return False
    
    try:
        # Step 1: Upload the PDF file
        print(f"📤 Uploading {PDF_FILE}...")
        
        with open(PDF_FILE, 'rb') as f:
            files = {'file': (PDF_FILE, f, 'application/pdf')}
            response = requests.post(f"{API_BASE_URL}/upload", files=files)
        
        if response.status_code != 200:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            return False
        
        upload_result = response.json()
        file_id = upload_result['file_id']
        print(f"✅ Upload successful! File ID: {file_id}")
        
        # Step 2: Validate the file
        print(f"🔍 Validating file...")
        response = requests.post(f"{API_BASE_URL}/validate?file_id={file_id}")
        
        if response.status_code != 200:
            print(f"❌ Validation failed: {response.status_code} - {response.text}")
            return False
        
        validation_result = response.json()
        print(f"✅ Validation successful: {validation_result['message']}")
        
        # Step 3: Process the receipt
        print(f"🔄 Processing receipt with OCR...")
        response = requests.post(f"{API_BASE_URL}/process?file_id={file_id}")
        
        if response.status_code != 200:
            print(f"❌ Processing failed: {response.status_code} - {response.text}")
            return False
        
        process_result = response.json()
        print(f"✅ Processing successful!")
        
        # Display the results
        print("\n📊 Extracted Receipt Data:")
        print("-" * 40)
        
        receipt_data = process_result.get('receipt_data', {})
        
        # Display basic info
        print(f"Store Name: {receipt_data.get('store_name', 'Not detected')}")
        print(f"Date: {receipt_data.get('date', 'Not detected')}")
        print(f"Total Amount: ${receipt_data.get('total_amount', 'Not detected')}")
        
        # Display items
        items = receipt_data.get('items', [])
        if items:
            print(f"\nItems ({len(items)}):")
            for i, item in enumerate(items, 1):
                name = item.get('name', 'Unknown item')
                price = item.get('price', 'Unknown price')
                print(f"  {i}. {name} - ${price}")
        else:
            print("\nNo items detected")
        
        # Display raw text (first 200 characters)
        raw_text = process_result.get('raw_text', '')
        if raw_text:
            print(f"\n📝 Raw OCR Text (first 200 chars):")
            print("-" * 40)
            print(raw_text[:200] + ("..." if len(raw_text) > 200 else ""))
        
        print("\n" + "=" * 60)
        print("✅ Test completed successfully! Tesseract is now working.")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API server.")
        print("Make sure the backend server is running on http://localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_pdf_processing()
    if not success:
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure the backend server is running: python run_backend.py")
        print("2. Check that the PDF file exists in the current directory")
        print("3. Verify Tesseract is properly configured in backend/ocr_utils.py")
