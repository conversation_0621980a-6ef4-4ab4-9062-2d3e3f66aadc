import pytesseract
from PIL import Image
import re
from datetime import datetime
from typing import Dict, Optional, List
import os
import fitz  # PyMuPDF - alternative to pdf2image
import io
import warnings

class ReceiptOCR:
    """OCR utility class for extracting text and data from receipt images"""
    
    def __init__(self):
        # Configure tesseract path if needed (Windows)
        # Uncomment and modify the path below if Tess<PERSON>ct is not in your system PATH
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        # Initialize EasyOCR as fallback (will be loaded on demand)
        self.easyocr_reader = None
        self.ocr_method = None  # Will be determined at runtime
    
    def pdf_to_images(self, pdf_path: str) -> List[Image.Image]:
        """Convert PDF to list of PIL Images using PyMuPDF (fallback from pdf2image)"""
        try:
            # Try using PyMuPDF first (doesn't require poppler)
            doc = fitz.open(pdf_path)
            images = []

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                # Render page to image
                mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better OCR quality
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                img = Image.open(io.BytesIO(img_data))
                images.append(img)

            doc.close()
            return images

        except Exception as e:
            # Fallback to pdf2image if PyMuPDF fails
            try:
                from pdf2image import convert_from_path
                images = convert_from_path(pdf_path)
                return images
            except ImportError:
                raise Exception("Neither PyMuPDF nor pdf2image with poppler is available. Please install PyMuPDF: pip install PyMuPDF")
            except Exception as pdf2img_error:
                raise Exception(f"Error converting PDF to images: PyMuPDF failed ({str(e)}), pdf2image failed ({str(pdf2img_error)}). Please install poppler for pdf2image or use PyMuPDF.")
    
    def _get_easyocr_reader(self):
        """Initialize EasyOCR reader on demand"""
        if self.easyocr_reader is None:
            try:
                import easyocr
                self.easyocr_reader = easyocr.Reader(['en'])
                print("✅ EasyOCR initialized successfully")
            except ImportError:
                raise Exception("EasyOCR not available. Install with: pip install easyocr")
        return self.easyocr_reader

    def extract_text_from_image(self, image: Image.Image) -> str:
        """Extract text from PIL Image using OCR (with fallback options)"""

        # Method 1: Try Tesseract OCR first
        try:
            text = pytesseract.image_to_string(image, config='--psm 6')
            self.ocr_method = "tesseract"
            return text
        except pytesseract.TesseractNotFoundError:
            print("⚠️  Tesseract OCR not found, trying EasyOCR fallback...")
        except Exception as tesseract_error:
            print(f"⚠️  Tesseract OCR failed: {tesseract_error}, trying EasyOCR fallback...")

        # Method 2: Fallback to EasyOCR
        try:
            import numpy as np
            reader = self._get_easyocr_reader()

            # Convert PIL Image to numpy array for EasyOCR
            img_array = np.array(image)
            results = reader.readtext(img_array)

            # Extract text from results
            text = ' '.join([result[1] for result in results])
            self.ocr_method = "easyocr"
            print(f"✅ EasyOCR extracted {len(text)} characters")
            return text

        except ImportError:
            raise Exception("Neither Tesseract nor EasyOCR is available. Please install one of them:\n"
                          "- Tesseract: See TESSERACT_SETUP.md\n"
                          "- EasyOCR: pip install easyocr")
        except Exception as easyocr_error:
            raise Exception(f"Both OCR methods failed:\n"
                          f"- Tesseract: Not found or failed\n"
                          f"- EasyOCR: {str(easyocr_error)}\n"
                          f"Please install Tesseract (see TESSERACT_SETUP.md) or ensure EasyOCR works properly.")
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF file using multiple methods"""
        try:
            # Method 1: Try direct text extraction from PDF (for text-based PDFs)
            try:
                doc = fitz.open(pdf_path)
                full_text = ""

                for page_num in range(len(doc)):
                    print(f"Extracting text from page {page_num + 1}...")
                    page = doc.load_page(page_num)
                    print(f"   Page size: {page.rect.width}x{page.rect.height}")
                    text = page.get_text()
                    print(f"   Text: {text}")
                    full_text += text + "\n"

                doc.close()

                # If we got meaningful text (more than just whitespace), use it
                if full_text.strip() and len(full_text.strip()) > 10:
                    return full_text.strip()

            except Exception as direct_error:
                print(f"Direct text extraction failed: {direct_error}")

            # Method 2: OCR from images (for scanned PDFs)
            try:
                images = self.pdf_to_images(pdf_path)
                full_text = ""

                for image in images:
                    text = self.extract_text_from_image(image)
                    full_text += text + "\n"

                return full_text.strip()

            except Exception as ocr_error:
                print(f"OCR extraction failed: {ocr_error}")

                # Method 3: Fallback - return basic info
                return f"PDF file: {pdf_path}\nText extraction failed. Please ensure Tesseract OCR is installed.\nSee TESSERACT_SETUP.md for installation instructions."

        except Exception as e:
            raise Exception(f"Error extracting text from PDF: {str(e)}")
    
    def parse_receipt_data(self, text: str) -> Dict:
        """Parse extracted text to extract structured receipt data"""
        data = {
            'merchant_name': None,
            'total_amount': None,
            'tax_amount': None,
            'subtotal': None,
            'purchased_at': None,
            'payment_method': None,
            'receipt_number': None,
            'store_address': None,
            'store_phone': None,
            'raw_text': text
        }
        
        # Clean text for better parsing
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        text_lower = text.lower()
        
        # Extract merchant name (usually first few lines)
        if lines:
            # Try to find merchant name in first 3 lines
            for i, line in enumerate(lines[:3]):
                if len(line) > 3 and not re.match(r'^\d+', line):
                    data['merchant_name'] = line
                    break
        
        # Extract total amount
        total_patterns = [
            r'total[:\s]*\$?(\d+\.?\d*)',
            r'amount[:\s]*\$?(\d+\.?\d*)',
            r'grand total[:\s]*\$?(\d+\.?\d*)',
            r'\$(\d+\.\d{2})\s*$'
        ]
        
        for pattern in total_patterns:
            match = re.search(pattern, text_lower)
            if match:
                try:
                    data['total_amount'] = float(match.group(1))
                    break
                except ValueError:
                    continue
        
        # Extract tax amount
        tax_patterns = [
            r'tax[:\s]*\$?(\d+\.?\d*)',
            r'hst[:\s]*\$?(\d+\.?\d*)',
            r'gst[:\s]*\$?(\d+\.?\d*)',
            r'pst[:\s]*\$?(\d+\.?\d*)'
        ]
        
        for pattern in tax_patterns:
            match = re.search(pattern, text_lower)
            if match:
                try:
                    data['tax_amount'] = float(match.group(1))
                    break
                except ValueError:
                    continue
        
        # Extract subtotal
        subtotal_patterns = [
            r'subtotal[:\s]*\$?(\d+\.?\d*)',
            r'sub total[:\s]*\$?(\d+\.?\d*)',
            r'sub-total[:\s]*\$?(\d+\.?\d*)'
        ]
        
        for pattern in subtotal_patterns:
            match = re.search(pattern, text_lower)
            if match:
                try:
                    data['subtotal'] = float(match.group(1))
                    break
                except ValueError:
                    continue
        
        # Extract date
        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
            r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})',
            r'(\w{3}\s+\d{1,2},?\s+\d{4})',
            r'(\d{1,2}\s+\w{3}\s+\d{4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    date_str = match.group(1)
                    # Try different date formats
                    for fmt in ['%m/%d/%Y', '%d/%m/%Y', '%Y/%m/%d', '%m-%d-%Y', '%d-%m-%Y', '%Y-%m-%d']:
                        try:
                            data['purchased_at'] = datetime.strptime(date_str, fmt)
                            break
                        except ValueError:
                            continue
                    if data['purchased_at']:
                        break
                except Exception:
                    continue
        
        # Extract payment method
        payment_patterns = [
            r'(visa|mastercard|amex|american express|discover|cash|debit|credit)',
            r'card[:\s]*(\*+\d{4})',
            r'payment[:\s]*method[:\s]*(\w+)'
        ]
        
        for pattern in payment_patterns:
            match = re.search(pattern, text_lower)
            if match:
                data['payment_method'] = match.group(1)
                break
        
        # Extract receipt number
        receipt_patterns = [
            r'receipt[:\s]*#?(\w+)',
            r'transaction[:\s]*#?(\w+)',
            r'ref[:\s]*#?(\w+)',
            r'#(\d+)'
        ]
        
        for pattern in receipt_patterns:
            match = re.search(pattern, text_lower)
            if match:
                data['receipt_number'] = match.group(1)
                break
        
        # Extract phone number
        phone_pattern = r'(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})'
        match = re.search(phone_pattern, text)
        if match:
            data['store_phone'] = match.group(1)
        
        # Extract address (simple approach - look for lines with numbers and street indicators)
        address_lines = []
        for line in lines:
            if re.search(r'\d+.*(?:st|street|ave|avenue|rd|road|blvd|boulevard|dr|drive|way|lane|ln)', line.lower()):
                address_lines.append(line)
        
        if address_lines:
            data['store_address'] = '\n'.join(address_lines[:2])  # Take first 2 address lines
        
        return data
    
    def process_receipt(self, pdf_path: str) -> Dict:
        """Complete receipt processing pipeline"""
        try:
            # Extract text from PDF
            text = self.extract_text_from_pdf(pdf_path)

            print(f'extract_text_from_pdf done : {text}')
            
            # Parse structured data
            data = self.parse_receipt_data(text)
            
            return data
        except Exception as e:
            raise Exception(f"Error processing receipt: {str(e)}")

# Global OCR instance
ocr_processor = ReceiptOCR()
