import streamlit as st
import requests
import pandas as pd
from datetime import datetime
import json

# Configuration
API_BASE_URL = "http://localhost:8000"

# Page configuration
st.set_page_config(
    page_title="Receipt Processing System",
    page_icon="🧾",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        margin: 1rem 0;
    }
    .error-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        margin: 1rem 0;
    }
    .info-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Helper functions
def make_api_request(endpoint, method="GET", data=None, files=None):
    """Make API request to backend"""
    url = f"{API_BASE_URL}{endpoint}"
    try:
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            if files:
                response = requests.post(url, files=files, data=data)
            else:
                response = requests.post(url, json=data)
        elif method == "DELETE":
            response = requests.delete(url)

        if response.status_code in [200, 204]:
            return True, response.json() if response.content else {"message": "Success"}
        else:
            return False, response.json() if response.content else {"error": f"HTTP {response.status_code}"}
    except requests.exceptions.ConnectionError:
        return False, {"error": "Cannot connect to API server. Please ensure the backend is running."}
    except Exception as e:
        return False, {"error": str(e)}

def display_receipt_data(receipt_data):
    """Display receipt data in a formatted way"""
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Basic Information")
        st.write(f"**Merchant:** {receipt_data.get('merchant_name', 'N/A')}")
        st.write(f"**Total Amount:** ${receipt_data.get('total_amount', 'N/A')}")
        st.write(f"**Tax Amount:** ${receipt_data.get('tax_amount', 'N/A')}")
        st.write(f"**Subtotal:** ${receipt_data.get('subtotal', 'N/A')}")
        
        if receipt_data.get('purchased_at'):
            purchase_date = datetime.fromisoformat(receipt_data['purchased_at'].replace('Z', '+00:00'))
            st.write(f"**Purchase Date:** {purchase_date.strftime('%Y-%m-%d %H:%M:%S')}")
    
    with col2:
        st.subheader("Additional Details")
        st.write(f"**Payment Method:** {receipt_data.get('payment_method', 'N/A')}")
        st.write(f"**Receipt Number:** {receipt_data.get('receipt_number', 'N/A')}")
        st.write(f"**Store Phone:** {receipt_data.get('store_phone', 'N/A')}")
        
        if receipt_data.get('store_address'):
            st.write(f"**Store Address:** {receipt_data.get('store_address')}")

# Main app
def main():
    st.markdown('<h1 class="main-header">🧾 Receipt Processing System</h1>', unsafe_allow_html=True)
    
    # Sidebar navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Upload & Process", "View All Receipts", "View All Files", "API Status"]
    )
    
    if page == "Upload & Process":
        upload_and_process_page()
    elif page == "View All Receipts":
        view_receipts_page()
    elif page == "View All Files":
        view_files_page()
    elif page == "API Status":
        api_status_page()

def upload_and_process_page():
    """Page for uploading and processing receipts"""
    st.header("Upload & Process Receipt")
    
    # File upload
    uploaded_file = st.file_uploader(
        "Choose a PDF receipt file",
        type=['pdf'],
        help="Upload a PDF file containing a scanned receipt"
    )
    
    if uploaded_file is not None:
        st.success(f"File selected: {uploaded_file.name}")

        # Combined Upload, Validate & Process button
        if st.button("🚀 Upload, Validate & Process Receipt", use_container_width=True, type="primary"):
            with st.spinner("Uploading, validating, and processing receipt..."):
                files = {"file": (uploaded_file.name, uploaded_file.getvalue(), "application/pdf")}
                success, result = make_api_request("/upload-validate-process", "POST", files=files)

                if success:
                    st.markdown(f'<div class="success-box">✅ {result["message"]}<br>File ID: {result["file_id"]}<br>Receipt ID: {result["receipt_id"]}</div>', unsafe_allow_html=True)

                    if result.get('extracted_data'):
                        st.subheader("Extracted Data")
                        display_receipt_data(result['extracted_data'])

                        # Show raw text in expander
                        if result['extracted_data'].get('raw_text'):
                            with st.expander("View Raw OCR Text"):
                                st.text(result['extracted_data']['raw_text'])
                else:
                    st.markdown(f'<div class="error-box">❌ Processing failed: {result.get("detail", result.get("error"))}</div>', unsafe_allow_html=True)

        st.divider()
        st.subheader("Individual Actions")
        st.caption("Use these if you need to perform actions separately")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📤 Upload File Only", use_container_width=True):
                with st.spinner("Uploading file..."):
                    files = {"file": (uploaded_file.name, uploaded_file.getvalue(), "application/pdf")}
                    success, result = make_api_request("/upload", "POST", files=files)

                    if success:
                        st.session_state.uploaded_file_id = result['file_id']
                        st.markdown(f'<div class="success-box">✅ {result["message"]}<br>File ID: {result["file_id"]}</div>', unsafe_allow_html=True)
                    else:
                        st.markdown(f'<div class="error-box">❌ Upload failed: {result.get("detail", result.get("error"))}</div>', unsafe_allow_html=True)

        with col2:
            if st.button("✅ Validate File Only", use_container_width=True):
                if 'uploaded_file_id' in st.session_state:
                    with st.spinner("Validating file..."):
                        success, result = make_api_request(f"/validate?file_id={st.session_state.uploaded_file_id}", "POST")

                        if success:
                            if result['is_valid']:
                                st.markdown(f'<div class="success-box">✅ {result["message"]}</div>', unsafe_allow_html=True)
                            else:
                                st.markdown(f'<div class="error-box">❌ {result["message"]}<br>Reason: {result.get("invalid_reason")}</div>', unsafe_allow_html=True)
                        else:
                            st.markdown(f'<div class="error-box">❌ Validation failed: {result.get("detail", result.get("error"))}</div>', unsafe_allow_html=True)
                else:
                    st.warning("Please upload a file first")

        with col3:
            if st.button("🔍 Process Receipt Only", use_container_width=True):
                if 'uploaded_file_id' in st.session_state:
                    with st.spinner("Processing receipt with OCR..."):
                        success, result = make_api_request(f"/process?file_id={st.session_state.uploaded_file_id}", "POST")

                        if success:
                            st.markdown(f'<div class="success-box">✅ {result["message"]}<br>Receipt ID: {result["receipt_id"]}</div>', unsafe_allow_html=True)

                            if result.get('extracted_data'):
                                st.subheader("Extracted Data")
                                display_receipt_data(result['extracted_data'])

                                # Show raw text in expander
                                if result['extracted_data'].get('raw_text'):
                                    with st.expander("View Raw OCR Text"):
                                        st.text(result['extracted_data']['raw_text'])
                        else:
                            st.markdown(f'<div class="error-box">❌ Processing failed: {result.get("detail", result.get("error"))}</div>', unsafe_allow_html=True)
                else:
                    st.warning("Please upload a file first")

def view_receipts_page():
    """Page for viewing all receipts"""
    st.header("All Processed Receipts")
    
    if st.button("🔄 Refresh Data"):
        st.rerun()
    
    with st.spinner("Loading receipts..."):
        success, result = make_api_request("/receipts")
        
        if success:
            if result:
                # Create DataFrame for display
                receipts_data = []
                for receipt in result:
                    receipts_data.append({
                        'ID': receipt['id'],
                        'Merchant': receipt.get('merchant_name', 'N/A'),
                        'Total Amount': f"${receipt.get('total_amount', 'N/A')}",
                        'Purchase Date': receipt.get('purchased_at', 'N/A')[:10] if receipt.get('purchased_at') else 'N/A',
                        'Payment Method': receipt.get('payment_method', 'N/A'),
                        'Receipt Number': receipt.get('receipt_number', 'N/A')
                    })
                
                df = pd.DataFrame(receipts_data)
                st.dataframe(df, use_container_width=True)
                
                # Detailed view
                st.subheader("Detailed View")
                selected_id = st.selectbox("Select receipt ID for details:", [r['id'] for r in result])
                
                if selected_id:
                    selected_receipt = next(r for r in result if r['id'] == selected_id)
                    display_receipt_data(selected_receipt)
            else:
                st.info("No receipts found. Upload and process some receipts first!")
        else:
            st.markdown(f'<div class="error-box">❌ Failed to load receipts: {result.get("detail", result.get("error"))}</div>', unsafe_allow_html=True)

def view_files_page():
    """Page for viewing all uploaded files"""
    st.header("All Uploaded Files")

    if st.button("🔄 Refresh Data"):
        st.rerun()

    with st.spinner("Loading files..."):
        success, result = make_api_request("/files")

        if success:
            if result:
                # Create DataFrame for display
                files_data = []
                for file in result:
                    files_data.append({
                        'ID': file['id'],
                        'File Name': file['file_name'],
                        'Valid': '✅' if file['is_valid'] else '❌' if file['is_valid'] is False else '⏳',
                        'Processed': '✅' if file['is_processed'] else '❌',
                        'Upload Date': file['created_at'][:10] if file.get('created_at') else 'N/A',
                        'Invalid Reason': file.get('invalid_reason', 'N/A') if file['is_valid'] is False else 'N/A'
                    })

                df = pd.DataFrame(files_data)

                # Display table with selection
                st.subheader("Select a file by clicking on a row")
                selected_rows = st.dataframe(
                    df,
                    use_container_width=True,
                    on_select="rerun",
                    selection_mode="single-row"
                )

                # Get selected file
                selected_file = None
                if selected_rows.selection.rows:
                    selected_row_index = selected_rows.selection.rows[0]
                    selected_file = result[selected_row_index]
                    st.success(f"Selected: {selected_file['file_name']} (ID: {selected_file['id']})")

                # File operations
                if selected_file:
                    st.subheader("File Operations")

                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        if st.button("🚀 Upload, Validate & Process", use_container_width=True, type="primary"):
                            with st.spinner("Processing all steps..."):
                                # First validate
                                success, validate_result = make_api_request(f"/validate?file_id={selected_file['id']}", "POST")
                                if success and validate_result['is_valid']:
                                    # Then process
                                    success, process_result = make_api_request(f"/process?file_id={selected_file['id']}", "POST")
                                    if success:
                                        st.success(f"✅ {process_result['message']}")
                                        if process_result.get('extracted_data'):
                                            st.subheader("Extracted Data")
                                            display_receipt_data(process_result['extracted_data'])
                                    else:
                                        st.error(f"❌ Processing failed: {process_result.get('detail', process_result.get('error'))}")
                                else:
                                    st.error(f"❌ Validation failed: {validate_result.get('invalid_reason', 'Unknown error')}")

                    with col2:
                        if st.button("✅ Validate File", use_container_width=True):
                            with st.spinner("Validating..."):
                                success, result_val = make_api_request(f"/validate?file_id={selected_file['id']}", "POST")
                                if success:
                                    if result_val['is_valid']:
                                        st.success(f"✅ {result_val['message']}")
                                    else:
                                        st.error(f"❌ {result_val['message']}: {result_val.get('invalid_reason')}")
                                else:
                                    st.error(f"❌ Validation failed: {result_val.get('detail', result_val.get('error'))}")

                    with col3:
                        if st.button("🔍 Process File", use_container_width=True):
                            with st.spinner("Processing..."):
                                success, result_proc = make_api_request(f"/process?file_id={selected_file['id']}", "POST")
                                if success:
                                    st.success(f"✅ {result_proc['message']}")
                                    if result_proc.get('extracted_data'):
                                        st.subheader("Extracted Data")
                                        display_receipt_data(result_proc['extracted_data'])
                                else:
                                    st.error(f"❌ Processing failed: {result_proc.get('detail', result_proc.get('error'))}")

                    with col4:
                        if st.button("🗑️ Delete File", use_container_width=True, type="secondary"):
                            if st.session_state.get('confirm_delete') != selected_file['id']:
                                st.session_state.confirm_delete = selected_file['id']
                                st.warning("⚠️ Click again to confirm deletion")
                            else:
                                with st.spinner("Deleting file..."):
                                    success, delete_result = make_api_request(f"/files/{selected_file['id']}", "DELETE")
                                    if success:
                                        st.success(f"✅ {delete_result['message']}")
                                        st.session_state.confirm_delete = None
                                        st.rerun()
                                    else:
                                        st.error(f"❌ Delete failed: {delete_result.get('detail', delete_result.get('error'))}")

                    # File details section
                    with st.expander("📄 View File Details"):
                        success, file_details = make_api_request(f"/files/{selected_file['id']}")
                        if success:
                            st.json(file_details)
                        else:
                            st.error(f"❌ Failed to get file details: {file_details.get('detail', file_details.get('error'))}")
                else:
                    st.info("👆 Select a file from the table above to perform operations")
            else:
                st.info("No files found. Upload some files first!")
        else:
            st.markdown(f'<div class="error-box">❌ Failed to load files: {result.get("detail", result.get("error"))}</div>', unsafe_allow_html=True)

def api_status_page():
    """Page for checking API status and health"""
    st.header("API Status & Health Check")

    if st.button("🔄 Check API Status"):
        with st.spinner("Checking API status..."):
            success, result = make_api_request("/")

            if success:
                st.markdown(f'<div class="success-box">✅ API is running<br>Message: {result.get("message")}<br>Version: {result.get("version")}</div>', unsafe_allow_html=True)
            else:
                st.markdown(f'<div class="error-box">❌ API is not accessible<br>Error: {result.get("error")}</div>', unsafe_allow_html=True)

    st.subheader("API Endpoints")
    endpoints = [
        {"Method": "POST", "Endpoint": "/upload", "Description": "Upload a receipt file"},
        {"Method": "POST", "Endpoint": "/validate", "Description": "Validate uploaded file"},
        {"Method": "POST", "Endpoint": "/process", "Description": "Process receipt with OCR"},
        {"Method": "POST", "Endpoint": "/upload-validate-process", "Description": "Combined: Upload, validate & process in one step"},
        {"Method": "GET", "Endpoint": "/receipts", "Description": "Get all receipts"},
        {"Method": "GET", "Endpoint": "/receipts/{id}", "Description": "Get specific receipt"},
        {"Method": "GET", "Endpoint": "/files", "Description": "Get all files"},
        {"Method": "GET", "Endpoint": "/files/{id}", "Description": "Get specific file"},
        {"Method": "DELETE", "Endpoint": "/files/{id}", "Description": "Delete a file and its data"},
    ]

    df_endpoints = pd.DataFrame(endpoints)
    st.dataframe(df_endpoints, use_container_width=True)

    st.subheader("Configuration")
    st.write(f"**API Base URL:** {API_BASE_URL}")
    st.write("**Supported File Types:** PDF")
    st.write("**OCR Engine:** Tesseract")

if __name__ == "__main__":
    main()
