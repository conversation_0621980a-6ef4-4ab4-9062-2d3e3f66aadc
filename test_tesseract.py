#!/usr/bin/env python3
"""
Test script to verify Tesseract OCR installation and configuration
"""

import sys
import os

# Add the backend directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    import pytesseract
    from PIL import Image
    
    # Set the tesseract path (same as in ocr_utils.py)
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    print("Testing Tesseract OCR installation...")
    print("=" * 50)
    
    # Test 1: Check if tesseract is accessible
    try:
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
    except Exception as e:
        print(f"❌ Tesseract version check failed: {e}")
        sys.exit(1)
    
    # Test 2: Check available languages
    try:
        languages = pytesseract.get_languages()
        print(f"✅ Available languages: {', '.join(languages[:5])}{'...' if len(languages) > 5 else ''}")
        print(f"   Total languages: {len(languages)}")
    except Exception as e:
        print(f"❌ Language check failed: {e}")
    
    # Test 3: Test OCR on a simple image (create a test image)
    try:
        # Create a simple test image with text
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a white image
        img = Image.new('RGB', (300, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add some text
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
            
        draw.text((10, 30), "Hello Tesseract!", fill='black', font=font)
        
        # Test OCR on this image
        text = pytesseract.image_to_string(img, config='--psm 6')
        print(f"✅ OCR test successful!")
        print(f"   Extracted text: '{text.strip()}'")
        
    except Exception as e:
        print(f"❌ OCR test failed: {e}")
    
    print("=" * 50)
    print("Tesseract test completed!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you have installed the required packages:")
    print("pip install pytesseract pillow")
    sys.exit(1)
