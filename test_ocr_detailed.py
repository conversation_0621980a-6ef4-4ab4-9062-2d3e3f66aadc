#!/usr/bin/env python3
"""
Detailed test to check OCR extraction from the problematic PDF
"""

import sys
import os

# Add the backend directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from ocr_utils import ReceiptOCR
    
    print("Testing OCR extraction directly...")
    print("=" * 50)
    
    # Initialize OCR processor
    ocr_processor = ReceiptOCR()
    
    # Test file
    pdf_file = "not_working_receipt.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"❌ PDF file not found: {pdf_file}")
        sys.exit(1)
    
    print(f"📄 Processing: {pdf_file}")
    
    try:
        # Extract text from PDF
        text = ocr_processor.extract_text_from_pdf(pdf_file)
        
        print(f"✅ OCR Method used: {ocr_processor.ocr_method}")
        print(f"📝 Extracted text length: {len(text)} characters")
        
        if text.strip():
            print("\n📝 Raw extracted text:")
            print("-" * 40)
            print(text)
            print("-" * 40)
        else:
            print("⚠️  No text was extracted from the PDF")
            
        # Try to process the receipt data
        print("\n🔄 Processing receipt data...")
        receipt_data = ocr_processor.process_receipt(pdf_file)
        
        print("📊 Processed receipt data:")
        print(f"  Store Name: {receipt_data.get('store_name', 'Not detected')}")
        print(f"  Date: {receipt_data.get('date', 'Not detected')}")
        print(f"  Total: ${receipt_data.get('total_amount', 'Not detected')}")
        print(f"  Items: {len(receipt_data.get('items', []))}")
        
    except Exception as e:
        print(f"❌ OCR processing failed: {e}")
        import traceback
        traceback.print_exc()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
